// Example usage of the modified hasPermission function
// This demonstrates how the function can now accept an optional projectRole parameter

import { usePermissions, DraftUserRole } from './packages/revo-web/src/hooks/useRoleAndPermission'

// Example component showing the usage
function ExampleComponent() {
  // Hook initialized with a default project role
  const { hasPermission } = usePermissions(DraftUserRole.PROJECT_DESIGNER)
  
  // Usage examples:
  
  // 1. Using the default project role (PROJECT_DESIGNER) from the hook
  const canEditWithDefault = hasPermission('editProject')
  // This will check permissions for PROJECT_DESIGNER role
  
  // 2. Overriding the project role for a specific check
  const canEditAsAdmin = hasPermission('editProject', DraftUserRole.PROJECT_ADMIN)
  // This will check permissions for PROJECT_ADMIN role instead
  
  // 3. Checking different permissions with different roles
  const canEditMembersAsVisitor = hasPermission('editMembers', DraftUserRole.VISITOR)
  // This will check if a VISITOR can edit members (should be false)
  
  const canViewAsVisitor = hasPermission('viewProject', DraftUserRole.VISITOR)
  // This will check if a VISITOR can view project (should be true)
  
  // 4. Backward compatibility - existing calls still work
  const canExportReport = hasPermission('exportReport')
  // Uses the default PROJECT_DESIGNER role from the hook
  
  return (
    <div>
      <p>Can edit with default role: {canEditWithDefault ? 'Yes' : 'No'}</p>
      <p>Can edit as admin: {canEditAsAdmin ? 'Yes' : 'No'}</p>
      <p>Can edit members as visitor: {canEditMembersAsVisitor ? 'Yes' : 'No'}</p>
      <p>Can view as visitor: {canViewAsVisitor ? 'Yes' : 'No'}</p>
      <p>Can export report: {canExportReport ? 'Yes' : 'No'}</p>
    </div>
  )
}

export default ExampleComponent
